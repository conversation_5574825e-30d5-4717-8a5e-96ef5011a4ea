#!/usr/bin/env python3
import os
import sys
import re
import subprocess
import argparse
import requests
import shutil
from pathlib import Path
from time import sleep


def usage():
    """Display usage information and exit"""
    print(f"Usage: {sys.argv[0]} <customer_name>")
    print(f"Example: {sys.argv[0]} demo")
    sys.exit(1)


def validate_customer_name(customer_name):
    """Validate customer name format (lowercase letters, numbers, hyphens only)"""
    if not re.match(r'^[a-z0-9-]+$', customer_name):
        print("Error: Customer name must contain only lowercase letters, numbers and hyphens")
        sys.exit(1)


def run_command(command, error_message):
    """Run a command and exit if it fails"""
    result = subprocess.run(command, capture_output=False, text=True)
    # Immer die Ausgabe anzeigen
    if result.stdout:
        print(f"Command output: {result.stdout}")
    if result.returncode != 0:
        print(f"Error: {error_message}")
        print(f"Command error: {result.stderr}")
        raise Exception(error_message)
    return result


# Cleanup functions for each step
def cleanup_step1(customer_name):
    """Clean up after step 1 - delete customer directory"""
    customer_dir = f"/mnt/storage/tenants/{customer_name}"
    try:
        print(f"Cleaning up step 1: Removing customer directory {customer_dir}")
        shutil.rmtree(customer_dir, ignore_errors=True)
    except Exception as e:
        print(f"Warning: Cleanup of step 1 failed: {str(e)}")


def cleanup_step2(customer_name, db_script):
    """Clean up after step 2 - reset database"""
    try:
        print(f"Cleaning up step 2: Resetting database for {customer_name}")
        subprocess.run(["python3", str(db_script), customer_name, "--reset"],
                       capture_output=False, text=True)
    except Exception as e:
        print(f"Warning: Cleanup of step 2 failed: {str(e)}")


def cleanup_step3(customer_name, dns_script):
    """Clean up after step 3 - delete DNS entries"""
    try:
        print(f"Cleaning up step 3: Removing DNS entries for {customer_name}")
        subprocess.run(["python3", str(dns_script), customer_name, "-d"],
                       capture_output=False, text=True)
    except Exception as e:
        print(f"Warning: Cleanup of step 3 failed: {str(e)}")


def cleanup_step4(customer_name, webproxy_script):
    """Clean up after step 4 - delete web proxy entry"""
    try:
        print(f"Cleaning up step 4: Removing web proxy entry for {customer_name}")
        subprocess.run(["python3", str(webproxy_script), customer_name, "-d"],
                       capture_output=False, text=True)
    except Exception as e:
        print(f"Warning: Cleanup of step 4 failed: {str(e)}")


def main():
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Setup a new tenant')
    parser.add_argument('customer_name', help='Name of the customer')
    
    # Handle case where no arguments are provided
    if len(sys.argv) == 1:
        usage()
    
    args = parser.parse_args()
    customer_name = args.customer_name
    
    # Validate customer name
    validate_customer_name(customer_name)
    
    # Define script paths
    script_dir = Path(os.path.dirname(os.path.abspath(__file__)))
    config_script = script_dir / "setup-tenant-config.py" # step 1
    db_script = script_dir / "setup-tenant-db.py" # step 2
    keycloak_script = script_dir / "setup-tenant-keycloak.py" # step 6
    dns_script = script_dir / "setup-tenant-dns.py" # step 3
    docker_script = script_dir / "setup-tenant-docker.py" # step 5a
    lldap_script = script_dir / "setup-tenant-lldap.py" # step 5b
    nextcloud_status_script = script_dir / "setup-tenant-nextcloud-status.py" # step 7
    webproxy_script = script_dir / "setup-tenant-webproxy.py" # step 4
    portal_script = script_dir / "setup-tenant-portal.py" # step 8
    
    # Track setup progress for cleanup
    setup_stage = 0
    
    try:
        # Step 1: Vorbereitung files/configs
        print(f"Step 1/8: Creating initial configuration for {customer_name}...")
        run_command(["python3", str(config_script), customer_name],
                    "Failed to create initial configuration")
        setup_stage = 1
        
        # Step 2: DB anlegen - first reset if needed
        print(f"Step 2/8: Setting up database for {customer_name}...")
        # Reset any existing database objects first
        #reset_result = subprocess.run(["python3", str(db_script), customer_name, "--reset"],
        #                            capture_output=False, text=True)
        # Then create new database objects
        run_command(["python3", str(db_script), customer_name],
                    "Failed to setup database")
        setup_stage = 2
        
        # Step 3: DNS anlegen
        print(f"Step 3/8: Setting up DNS records for {customer_name}...")
        run_command(["python3", str(dns_script), customer_name],
                    "Failed to create DNS records")
        setup_stage = 3
        
        # Step 4: Webproxy anlegen mit API-Call
        print(f"Step 4/8: Setting up web proxy for {customer_name}...")
        run_command(["python3", str(webproxy_script), customer_name],
                    "Failed to setup web proxy")

        setup_stage = 4

        sleep(15)

        # Step 5: Deployment Docker Stack via Portainer API
        # setup-tenant-docker.py {customer_name}
        run_command(["python3", str(docker_script), customer_name],
                    "Failed to create Docker Stack")

        setup_stage = 5

        sleep(60)

        # Step 6: LLDAP bootstrap script ausführen via Portainer API 
        print(f"Step 6/8: Executing LLDAP bootstrap script for {customer_name}...")
        run_command(["python3", str(lldap_script), customer_name],
                    "Failed to setup LLDAP")
  
        setup_stage = 6
        
        # Step 7: Keycloak setup
        print(f"Step 7/8: Setting up Keycloak for {customer_name}...")
        run_command(["python3", str(keycloak_script), customer_name],
                    "Failed to setup Keycloak")
        setup_stage = 7
                
        # Step 8: Verify Nextcloud setup
        # setup-tenant-nextcloud-status.py {customer_name} nextcloud_status_script
        print(f"Step 8/8: Setting up Nextcloud for {customer_name}...")
        run_command(["python3", str(nextcloud_status_script), customer_name],
                    "Failed to setup Nextcloud")
        setup_stage = 8

        # setup-tenant-nextcloud-status.py {customer_name} nextcloud_status_script
        print(f"Step 9/9: Setting up Nextcloud for {customer_name}...")
        run_command(["python3", str(portal_script), customer_name],
                    "Failed to setup Portal-Leos360")
        setup_stage = 9        

        print("Service URLs:")
        print(f"- Nextcloud: https://{customer_name}.leos360.cloud")
        print(f"- Keycloak : https://{customer_name}-sso.leos360.cloud")
        print(f"- LLDAP API: https://{customer_name}-api.leos360.cloud")
        print(f"- LEOS360 Portal: https://portal-dev.leos360.com")
        
    except Exception as e:
        print(f"Setup failed at stage {setup_stage}: {str(e)}")
        
        # Perform cleanup based on how far we got
        if setup_stage >= 4:
            print("Cleaning up step 4: Web proxy")
            cleanup_step4(customer_name, webproxy_script)
            
        if setup_stage >= 3:
            print("Cleaning up step 3: DNS entries")
            cleanup_step3(customer_name, dns_script)
            
        if setup_stage >= 2:
            print("Cleaning up step 2: Database")
            cleanup_step2(customer_name, db_script)
            
        if setup_stage >= 1:
            print("Cleaning up step 1: Configuration files")
            cleanup_step1(customer_name)
            
        print(f"Cleanup completed. Setup for {customer_name} has been rolled back.")
        sys.exit(1)


if __name__ == "__main__":
    main()
