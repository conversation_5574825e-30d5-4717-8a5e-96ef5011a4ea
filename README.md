# LEOS360 Platform - Tenant Setup Scripts

This repository contains the automated tenant setup scripts for the LEOS360 platform. These scripts handle the complete lifecycle of tenant provisioning, from initial configuration to full deployment.

## Overview

The LEOS360 platform uses a multi-stage approach for tenant setup, with each stage handling specific aspects of the provisioning process:

- **Stage 1**: Tenant Configuration Setup
- **Stage 2**: Database and DNS Setup
- **Stage 3**: Docker Deployment
- **Stage 4**: Service Configuration (Keycloak, LLDAP, Portal)

## Prerequisites

### System Requirements
- Linux-based system with root access
- Python 3.12 or higher
- Docker and Docker Compose
- PostgreSQL client tools
- Network access to required services

### Required Directories and Files
```
/mnt/storage/setup/          # Master configuration templates
├── .env                     # Environment template
├── docker-compose.yml       # Docker Compose template
├── ssl/                     # SSL certificates
├── db/
│   └── db_setup.sql.template
├── dovecot/
│   ├── dovecot.conf
│   ├── dovecot-ldap-userdb.conf
│   ├── dovecot-ldap-passdb.conf
│   └── ...
├── postfix/
│   └── ldap/
├── keycloak/
├── nextcloud/
└── lldap/
    └── bootstrap/

/mnt/storage/tenants/        # Tenant configurations
```

## Stage Scripts

### Stage 1: Tenant Configuration Setup

**File**: `stage1_tenant_config.py`

**Purpose**: Creates the initial configuration structure for a new tenant.

**Features**:
- Validates prerequisites and system requirements
- Allocates unique IP addresses and Redis database indices
- Generates secure passwords for all services
- Creates directory structure for all services
- Sets up configuration files with proper variable substitution
- Copies SSL certificates and templates

**Usage**:
```bash
sudo python3 stage1_tenant_config.py <customer_name>
```

**Example**:
```bash
sudo python3 stage1_tenant_config.py example-customer
```

**Output**:
- Creates `/mnt/storage/tenants/<customer_name>/` directory structure
- Generates `.env` file with all configuration variables
- Creates `.secrets/credentials.txt` with generated passwords
- Sets up service-specific configuration files

### Stage 2: Database and Infrastructure Setup

**Files**:
- `stage2_tenant_db.py` - Database setup
- `stage2_tenant_dns.py` - DNS configuration
- `stage2_tenant_webproxy.py` - Web proxy setup

**Purpose**: Sets up external infrastructure components.

**Database Setup**:
```bash
python3 stage2_tenant_db.py <customer_name>
```

**DNS Setup**:
```bash
python3 stage2_tenant_dns.py <customer_name>
```

### Stage 3: Docker Deployment

**File**: `stage3_tenant_docker.py`

**Purpose**: Deploys the Docker stack for the tenant.

**Usage**:
```bash
python3 stage3_tenant_docker.py <customer_name>
```

### Stage 4: Service Configuration

**Files**:
- `stage4_tenant_keycloak.py` - Keycloak realm and client setup
- `stage4_tenant_lldap.py` - LLDAP user directory setup
- `stage4_tenant_portal.py` - Portal integration setup

**Purpose**: Configures individual services after deployment.

## Configuration Structure

Each tenant gets a dedicated directory structure:

```
/mnt/storage/tenants/<customer_name>/
├── .env                     # Environment variables
├── .secrets/
│   └── credentials.txt      # Generated passwords
├── docker-compose.yml       # Docker Compose configuration
├── ssl/                     # SSL certificates
├── nextcloud/
│   ├── data/
│   ├── config/
│   └── setup/
├── keycloak/
│   ├── data/
│   └── config/
├── lldap/
│   ├── data/
│   └── config/
├── dovecot/
│   ├── data/
│   └── config/
├── postfix/
│   ├── data/
│   └── config/
├── db/
│   └── db_setup_<customer_name>.sql
└── redis/
    └── data/
```

## Network Configuration

- **IP Range**: *********** - ************
- **Base Domain**: leos360.cloud
- **Customer Domain**: `<customer_name>.leos360.cloud`
- **Database Host**: ************:5432

## Security Features

- Secure password generation using Python's `secrets` module
- Proper file permissions (644 for files, 755 for directories, 640 for secrets)
- Isolated tenant configurations
- SSL certificate management
- Database user isolation

## Error Handling

All scripts include comprehensive error handling:
- Prerequisite validation
- Rollback on failure
- Detailed logging and status messages
- Cleanup procedures

## Troubleshooting

### Common Issues

1. **Permission Denied**: Ensure scripts are run with root privileges
2. **Template Not Found**: Verify master templates exist in `/mnt/storage/setup/`
3. **IP Address Exhaustion**: Check available IP range (***********-150)
4. **Database Connection**: Verify PostgreSQL connectivity to ************

### Log Messages

Scripts use standardized logging:
- `[INFO]` - General information
- `[SUCCESS]` - Successful operations
- `[WARNING]` - Non-critical issues
- `[ERROR]` - Critical errors requiring attention

## Development Guidelines

### Code Structure Template

All stage scripts should follow this structure:

```python
#!/usr/bin/env python3.12
"""
LEOS360 Platform - Stage X: Description
=====================================

Brief description of what this stage does.

Author: LEOS360 Development Team
Version: X.X
Last Updated: YYYY-MM-DD
"""

# Imports
import os
import sys
# ... other imports

# =============================================================================
# CONSTANTS AND CONFIGURATION
# =============================================================================

SCRIPT_VERSION = "X.X"
SCRIPT_NAME = "Stage X: Description"

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

def print_header(title: str) -> None:
    """Print a formatted section header."""
    print(f"\n{'='*60}")
    print(f" {title}")
    print(f"{'='*60}")

# ... other utility functions

# =============================================================================
# MAIN CLASS
# =============================================================================

class StageXSetup:
    """Main class for Stage X operations."""

    def __init__(self, customer_name: str) -> None:
        """Initialize setup."""
        pass

    def run(self) -> bool:
        """Main execution method."""
        pass

# =============================================================================
# MAIN SCRIPT ENTRY POINT
# =============================================================================

def main() -> None:
    """Main function."""
    pass

if __name__ == "__main__":
    main()
```

### Best Practices

1. **Consistent Naming**: Use clear, descriptive variable and function names
2. **Type Hints**: Include type hints for all function parameters and returns
3. **Documentation**: Comprehensive docstrings for all classes and methods
4. **Error Handling**: Proper exception handling with cleanup procedures
5. **Logging**: Use standardized print functions for consistent output
6. **Validation**: Validate all inputs and prerequisites
7. **Permissions**: Set appropriate file and directory permissions

### Section Organization

Each script should be organized into clearly marked sections:

1. **File Header**: Script description, version, and metadata
2. **Constants**: Configuration values and script settings
3. **Utility Functions**: Common helper functions
4. **Main Class**: Core functionality implementation
5. **Entry Point**: Argument parsing and main execution

## Quick Start

To set up a new tenant from scratch:

```bash
# Stage 1: Create configuration
sudo python3 stage1_tenant_config.py my-customer

# Stage 2: Set up database and DNS
python3 stage2_tenant_db.py my-customer
python3 stage2_tenant_dns.py my-customer

# Stage 3: Deploy Docker stack
python3 stage3_tenant_docker.py my-customer

# Stage 4: Configure LLDAP
python3 stage4_tenant_lldap.py my-customer
# Stage 4b: Configure Keycloak
python3 stage4_tenant_keycloak.py my-customer
python3 stage4_tenant_portal.py my-customer
```

## Support

For issues or questions regarding the tenant setup scripts, please contact the LEOS360 development team.

## Version History

- **v2.1** - Updated all stage files with enhanced functionality and improved consistency
- **v2.0** - Complete restructure with improved error handling and documentation
- **v1.x** - Initial implementation

---

**Note**: This documentation reflects the current state of the tenant setup scripts. Always refer to the individual script files for the most up-to-date usage information and requirements.
